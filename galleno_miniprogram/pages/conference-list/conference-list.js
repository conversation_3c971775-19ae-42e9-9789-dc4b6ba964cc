// conference-list.js
const util = require('../../utils/util.js')
const api = require('../../utils/api.js')

Page({
  data: {
    currentFilter: 'all',
    conferences: [],
    filteredConferences: [],
    loading: false
  },

  onLoad: function (options) {
    console.log('conference-list onLoad');

    // 获取系统信息进行自适应
    const systemInfo = util.getSystemInfo()
    console.log('系统信息:', systemInfo)

    this.setData({
      isSmallScreen: util.isSmallScreen(),
      isLargeScreen: util.isLargeScreen()
    });

    // 测试API配置
    console.log('API配置:', api.config);
    console.log('完整API URL:', api.config.baseUrl + '/hotel/conferences');

    // 直接测试API调用
    this.testApiCall();

    // 加载会议数据
    this.loadConferences();
  },

  // 测试API调用
  testApiCall: function() {
    console.log('开始测试API调用...');

    // 使用原生wx.request测试
    wx.request({
      url: api.config.baseUrl + '/hotel/conferences',
      method: 'GET',
      header: {
        'Content-Type': 'application/json'
      },
      success: (res) => {
        console.log('原生API测试成功:', res);
      },
      fail: (err) => {
        console.error('原生API测试失败:', err);
      }
    });
  },

  // 加载会议数据
  loadConferences: function() {
    this.setData({ loading: true });

    api.API.conference.getConferences()
      .then(res => {
        console.log('获取会议列表成功:', res);

        // API工具已经解析了数据，res就是数据本身，不需要再访问res.data
        const backendData = Array.isArray(res) ? res : (res.data || []);
        console.log('后端数据:', backendData);

        // 转换后端数据格式为前端需要的格式
        const conferences = this.transformConferenceData(backendData);
        console.log('转换后的会议数据:', conferences);

        this.setData({
          conferences: conferences,
          filteredConferences: conferences,
          loading: false
        });

        console.log('setData后的数据状态:', {
          conferences: this.data.conferences,
          filteredConferences: this.data.filteredConferences,
          loading: this.data.loading
        });

        // 应用当前筛选条件
        this.filterConferences(null);
        this.animateCards();
      })
      .catch(err => {
        console.error('获取会议列表失败:', err);
        this.setData({ loading: false });
        util.showMessage('获取会议列表失败，请稍后重试', 'error');
      });
  },

  // 转换后端数据格式
  transformConferenceData: function(backendData) {
    if (!Array.isArray(backendData)) {
      console.warn('后端数据不是数组格式:', backendData);
      return [];
    }

    return backendData.map((item, index) => {
      console.log(`转换会议数据 ${index + 1}:`, item);
      // 根据会议启用状态和时间确定前端状态
      let status = 'upcoming';
      let statusIcon = '⏰';
      let statusText = '即将开始';
      let roomStatus = 'pending';
      let roomStatusText = '预订即将开放';

      const now = new Date();
      const startTime = item.startTime ? new Date(item.startTime) : null;
      const endTime = item.endTime ? new Date(item.endTime) : null;
      const bookingOpenTime = item.bookingOpenTime ? new Date(item.bookingOpenTime) : null;
      const bookingCloseTime = item.bookingCloseTime ? new Date(item.bookingCloseTime) : null;

      // 判断会议状态
      // 后端可能返回enable字段为'Y'/'N'字符串，或者enable()方法的boolean值
      const isEnabled = item.enable === 'Y' || item.enable === true;
      console.log(`会议 ${item.conferenceTitle} 启用状态:`, item.enable, '-> 解析为:', isEnabled);

      if (isEnabled) {
        if (endTime && now > endTime) {
          // 会议已结束
          status = 'ended';
          statusIcon = '✅';
          statusText = '已结束';
          roomStatus = 'ended';
          roomStatusText = '会议已结束';
        } else if (startTime && now >= startTime && (!endTime || now <= endTime)) {
          // 会议进行中
          status = 'ongoing';
          statusIcon = '▶️';
          statusText = '进行中';
          roomStatus = 'full';
          roomStatusText = '房间已满';
        } else if (bookingOpenTime && bookingCloseTime && now >= bookingOpenTime && now <= bookingCloseTime) {
          // 可预订状态
          status = 'bookable';
          statusIcon = '⭐';
          statusText = '可预订';
          roomStatus = 'available';
          roomStatusText = '酒店房间充足';
        }
      } else {
        // 未启用的会议
        status = 'ended';
        statusIcon = '❌';
        statusText = '未启用';
        roomStatus = 'ended';
        roomStatusText = '会议未启用';
      }

      return {
        id: item.id,
        title: item.conferenceTitle || '未命名会议',
        date: this.formatConferenceDate(item.startTime, item.endTime),
        location: item.address || '待定',
        image: item.photoPath ? `http://**************${item.photoPath}` : 'https://images.unsplash.com/photo-1540575467063-178a50c2df87?w=400&h=200&fit=crop',
        status: status,
        statusIcon: statusIcon,
        statusText: statusText,
        participants: item.participantCount || '0',
        roomStatus: roomStatus,
        roomStatusText: roomStatusText,
        isFavorite: false,
        sortOrder: index + 1
      };
    });
  },

  // 格式化会议日期
  formatConferenceDate: function(startTime, endTime) {
    if (!startTime) return '时间待定';

    const start = new Date(startTime);
    const end = endTime ? new Date(endTime) : null;

    const formatDate = (date) => {
      return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
    };

    if (end && start.getTime() !== end.getTime()) {
      return `${formatDate(start)}-${formatDate(end)}`;
    } else {
      return formatDate(start);
    }
  },

  onShow: function () {
    // 页面显示时刷新数据
    if (this.data.conferences.length > 0) {
      this.filterConferences(null);
    } else {
      this.loadConferences();
    }
  },

  // 下拉刷新
  onRefresh: function() {
    this.loadConferences();
  },

  // 筛选会议
  filterConferences: function (e) {
    const type = e && e.currentTarget && e.currentTarget.dataset ? e.currentTarget.dataset.type : this.data.currentFilter;
    console.log('筛选会议，类型:', type);
    let filtered = [];

    if (type === 'all') {
      filtered = this.data.conferences;
    } else {
      filtered = this.data.conferences.filter(item => item.status === type);
    }

    // 按状态和排序优先级排序
    filtered.sort((a, b) => {
      // 可预订的会议置顶
      if (a.status === 'bookable' && b.status !== 'bookable') return -1;
      if (b.status === 'bookable' && a.status !== 'bookable') return 1;
      
      // 其他按sortOrder排序
      return a.sortOrder - b.sortOrder;
    });

    console.log('筛选结果:', {
      type: type,
      originalCount: this.data.conferences.length,
      filteredCount: filtered.length,
      filtered: filtered
    });

    this.setData({
      currentFilter: type,
      filteredConferences: filtered
    });
  },

  // 跳转到会议详情或识别码页面
  goToConference: function (e) {
    const conference = e.currentTarget.dataset.conference;
    
    if (conference.status === 'ended') {
      this.showMessage('该会议已结束，无法进行预订', 'warning');
      return;
    }

    if (conference.status === 'bookable') {
      // 可预订会议，跳转到识别码输入页面
      wx.navigateTo({
        url: `/pages/conference-code/conference-code?conferenceId=${conference.id}&title=${encodeURIComponent(conference.title)}`
      });
    } else if (conference.status === 'ongoing') {
      if (conference.roomStatus === 'full') {
        this.showMessage('该会议正在进行中，房间已满，无法预订', 'warning');
      } else {
        this.showMessage('该会议正在进行中，请联系客服了解预订情况', 'info');
      }
    } else if (conference.status === 'upcoming') {
      this.showMessage('该会议预订尚未开放，敬请期待', 'info');
    }
  },

  // 切换收藏状态
  toggleFavorite: function (e) {
    const id = e.currentTarget.dataset.id;
    const conferences = this.data.conferences.map(item => {
      if (item.id === id) {
        item.isFavorite = !item.isFavorite;
      }
      return item;
    });

    // 更新筛选后的列表
    const filteredConferences = this.data.filteredConferences.map(item => {
      if (item.id === id) {
        item.isFavorite = !item.isFavorite;
      }
      return item;
    });

    this.setData({
      conferences,
      filteredConferences
    });

    const message = conferences.find(item => item.id === id).isFavorite ? '已添加到收藏' : '已取消收藏';
    this.showMessage(message, 'success');
  },

  // 显示消息提示
  showMessage: function (message, type = 'info') {
    let icon = 'none';
    if (type === 'success') icon = 'success';
    if (type === 'warning') icon = 'none';
    if (type === 'info') icon = 'none';

    wx.showToast({
      title: message,
      icon: icon,
      duration: 2000
    });
  },

  // 卡片动画
  animateCards: function () {
    // 微信小程序中的动画效果可以通过CSS transition实现
    // 这里主要是为了触发页面渲染
    setTimeout(() => {
      this.setData({
        animationReady: true
      });
    }, 100);
  },

  // 下拉刷新
  onPullDownRefresh: function () {
    setTimeout(() => {
      this.filterConferences(null);
      wx.stopPullDownRefresh();
    }, 1000);
  },

  // 分享功能
  onShareAppMessage: function () {
    return {
      title: '会议酒店预定平台',
      path: '/pages/conference-list/conference-list'
    };
  }
});
